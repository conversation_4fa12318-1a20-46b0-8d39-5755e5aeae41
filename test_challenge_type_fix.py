#!/usr/bin/env python3
"""Test script to verify the challenge type fix works correctly."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.challenge import ChallengeType

def test_challenge_type_normalization():
    """Test that challenge type normalization works correctly."""
    
    # Test cases that should work
    test_cases = [
        ("algorithms", "algorithms"),
        ("ALGORITHMS", "algorithms"),
        ("Algorithms", "algorithms"),
        ("  algorithms  ", "algorithms"),
        ("data_structures", "data_structures"),
        ("DATA_STRUCTURES", "data_structures"),
        ("dynamic_programming", "dynamic_programming"),
        ("DYNAMIC_PROGRAMMING", "dynamic_programming"),
    ]
    
    print("Testing challenge type normalization...")
    
    for input_val, expected in test_cases:
        # Simulate the normalization logic from challenge_creator.py
        challenge_type_str = input_val.lower().strip()
        if challenge_type_str.startswith("challengetype."):
            challenge_type_str = challenge_type_str.replace("challengetype.", "")
        
        # Validate against known challenge types
        valid_types = [e.value for e in ChallengeType]
        if challenge_type_str not in valid_types:
            challenge_type_str = "algorithms"  # fallback
        
        try:
            # Try to create the enum
            challenge_type = ChallengeType(challenge_type_str)
            print(f"✅ '{input_val}' -> '{challenge_type_str}' -> {challenge_type}")
            assert challenge_type_str == expected, f"Expected {expected}, got {challenge_type_str}"
        except ValueError as e:
            print(f"❌ '{input_val}' -> '{challenge_type_str}' -> ERROR: {e}")
            return False
    
    # Test invalid cases
    invalid_cases = ["invalid_type", "INVALID", "something_else"]
    
    print("\nTesting invalid challenge types (should fallback to 'algorithms')...")
    
    for input_val in invalid_cases:
        challenge_type_str = input_val.lower().strip()
        if challenge_type_str.startswith("challengetype."):
            challenge_type_str = challenge_type_str.replace("challengetype.", "")
        
        # Validate against known challenge types
        valid_types = [e.value for e in ChallengeType]
        if challenge_type_str not in valid_types:
            challenge_type_str = "algorithms"  # fallback
        
        try:
            challenge_type = ChallengeType(challenge_type_str)
            print(f"✅ '{input_val}' -> '{challenge_type_str}' -> {challenge_type} (fallback)")
            assert challenge_type_str == "algorithms", f"Expected fallback to algorithms, got {challenge_type_str}"
        except ValueError as e:
            print(f"❌ '{input_val}' -> '{challenge_type_str}' -> ERROR: {e}")
            return False
    
    print("\n🎉 All challenge type normalization tests passed!")
    return True

if __name__ == "__main__":
    success = test_challenge_type_normalization()
    sys.exit(0 if success else 1)

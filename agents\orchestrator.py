"""Orchestrator Agent - Manages the challenge-solution iteration loop."""

import asyncio
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime

from models.challenge import Challenge, Solution, IterationRecord
from models.message import Message, MessageType, AgentType
from core.message_system import message_bus, MessageFactory
from core.logger import logger
from config.settings import settings

class OrchestratorAgent:
    """Agent responsible for orchestrating the challenge-solution iteration loop."""
    
    def __init__(self):
        self.agent_type = AgentType.ORCHESTRATOR
        self.current_iteration = 0
        self.max_iterations = settings.MAX_ITERATIONS
        self.iteration_records: List[IterationRecord] = []
        self.running = False
        
        # State tracking
        self.current_challenge: Optional[Challenge] = None
        self.current_solution: Optional[Solution] = None
        self.pending_responses = {}
        
        # Subscribe to message bus
        message_bus.subscribe(self.agent_type, self.handle_message)
    
    async def handle_message(self, message: Message):
        """Handle incoming messages."""
        try:
            if message.type == MessageType.CHALLENGE_RESPONSE:
                await self._handle_challenge_response(message)
            elif message.type == MessageType.SOLUTION_RESPONSE:
                await self._handle_solution_response(message)
            elif message.type == MessageType.ERROR:
                await self._handle_error_message(message)
            else:
                logger.log_error(f"Unknown message type: {message.type}", str(self.agent_type))
        except Exception as e:
            logger.log_error(f"Error handling message: {str(e)}", str(self.agent_type))
    
    async def _handle_challenge_response(self, message: Message):
        """Handle challenge response from Challenge Creator."""
        challenge_data = message.content.get("challenge")
        if not challenge_data:
            logger.log_error("No challenge data in response")
            return
        
        self.current_challenge = Challenge(**challenge_data)
        logger.logger.info(f"Received challenge: {self.current_challenge.title}")
        
        # Send challenge to Problem Solver
        solver_message = MessageFactory.create_challenge_response(
            self.agent_type, AgentType.PROBLEM_SOLVER, challenge_data
        )
        await message_bus.publish(solver_message)
    
    async def _handle_solution_response(self, message: Message):
        """Handle solution response from Problem Solver."""
        solution_data = message.content.get("solution")
        if not solution_data:
            logger.log_error("No solution data in response")
            return
        
        self.current_solution = Solution(**solution_data)
        logger.logger.info(f"Received solution for challenge: {self.current_solution.challenge_id}")
        
        # Complete current iteration
        await self._complete_iteration()
        
        # Start next iteration if not at max
        if self.current_iteration < self.max_iterations and self.running:
            await self._start_next_iteration()
        else:
            await self._finish_session()
    
    async def _handle_error_message(self, message: Message):
        """Handle error messages from other agents."""
        error = message.content.get("error", "Unknown error")
        context = message.content.get("context", {})
        
        logger.log_error(f"Agent error from {message.sender}: {error}")
        
        # Decide whether to retry or abort based on error type
        # For now, we'll log and continue
        if self.running:
            logger.logger.info("Attempting to continue despite error...")
    
    async def start_session(self, initial_challenge_type: Optional[str] = None,
                          initial_difficulty: str = "beginner"):
        """Start a new challenge-solution session."""
        if self.running:
            logger.log_error("Session already running")
            return
        
        self.running = True
        self.current_iteration = 0
        self.iteration_records = []
        
        logger.logger.info("Starting new challenge-solution session")
        logger.console.print("\n[bold green]🚀 Starting Multi-Agent Challenge Session[/bold green]")
        logger.console.print(f"Max iterations: {self.max_iterations}")
        logger.console.print(f"Initial challenge type: {initial_challenge_type or 'Any'}")
        logger.console.print(f"Initial difficulty: {initial_difficulty}")
        
        # Request initial challenge
        await self._request_initial_challenge(initial_challenge_type, initial_difficulty)
    
    async def _request_initial_challenge(self, challenge_type: Optional[str] = None,
                                       difficulty: str = "beginner"):
        """Request the initial challenge from Challenge Creator."""
        self.current_iteration = 1
        logger.log_iteration_start(self.current_iteration)
        
        challenge_request = MessageFactory.create_challenge_request(
            self.agent_type, AgentType.CHALLENGE_CREATOR, challenge_type, difficulty
        )
        await message_bus.publish(challenge_request)
    
    async def _complete_iteration(self):
        """Complete the current iteration and record it."""
        if not self.current_challenge or not self.current_solution:
            logger.log_error("Cannot complete iteration: missing challenge or solution")
            return
        
        # Create iteration record
        record = IterationRecord(
            iteration_number=self.current_iteration,
            challenge=self.current_challenge,
            solution=self.current_solution,
            enhancement_reasoning=f"Enhanced based on {self.current_solution.approach} approach"
        )
        
        self.iteration_records.append(record)
        logger.log_iteration_complete(record)
        
        # Display iteration summary
        logger.console.print(f"\n[bold blue]📊 Iteration {self.current_iteration} Summary[/bold blue]")
        logger.console.print(f"Challenge: {self.current_challenge.title}")
        logger.console.print(f"Difficulty: {self.current_challenge.difficulty}")
        logger.console.print(f"Solution Approach: {self.current_solution.approach}")
        logger.console.print(f"Time Complexity: {self.current_solution.time_complexity}")
        logger.console.print(f"Space Complexity: {self.current_solution.space_complexity}")
    
    async def _start_next_iteration(self):
        """Start the next iteration by requesting challenge enhancement."""
        if not self.current_challenge or not self.current_solution:
            logger.log_error("Cannot start next iteration: missing challenge or solution")
            return

        self.current_iteration += 1
        logger.log_iteration_start(self.current_iteration)

        # Request enhanced challenge
        enhancement_request = MessageFactory.create_enhancement_request(
            self.agent_type, AgentType.CHALLENGE_CREATOR,
            self.current_challenge.id, self.current_solution.model_dump()
        )
        await message_bus.publish(enhancement_request)
        
        # Reset current state for next iteration
        self.current_challenge = None
        self.current_solution = None
    
    async def _finish_session(self):
        """Finish the current session."""
        self.running = False
        
        logger.console.print("\n[bold green]🎉 Session Complete![/bold green]")
        logger.console.print(f"Completed {len(self.iteration_records)} iterations")
        
        # Display final summary
        logger.display_summary()
        
        # Save session data
        logger.save_session()
        
        logger.logger.info("Challenge-solution session completed")
    
    def stop_session(self):
        """Stop the current session."""
        if not self.running:
            logger.log_error("No session currently running")
            return
        
        self.running = False
        logger.logger.info("Session stopped by user")
        logger.console.print("[yellow]Session stopped by user[/yellow]")
    
    def get_session_status(self) -> Dict[str, Any]:
        """Get current session status."""
        return {
            "running": self.running,
            "current_iteration": self.current_iteration,
            "max_iterations": self.max_iterations,
            "completed_iterations": len(self.iteration_records),
            "current_challenge": self.current_challenge.model_dump() if self.current_challenge else None,
            "current_solution": self.current_solution.model_dump() if self.current_solution else None
        }
    
    def get_iteration_history(self) -> List[Dict[str, Any]]:
        """Get history of all completed iterations."""
        return [record.model_dump() for record in self.iteration_records]
